<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSFGM-Model 抗菌肽预测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            margin-bottom: 20px;
        }

        .status-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        textarea, input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            font-family: 'Courier New', monospace;
        }

        textarea:focus, input[type="text"]:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        button {
            flex: 1;
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #666;
            border: 2px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            display: none;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result.success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }

        .result.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .result.loading {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .result h3 {
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .result-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 6px;
            backdrop-filter: blur(10px);
        }

        .result-item strong {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
            opacity: 0.8;
        }

        .result-value {
            font-size: 1.1em;
            font-weight: 600;
        }

        .example-sequences {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .example-sequences h4 {
            margin-bottom: 15px;
            color: #555;
        }

        .example-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .example-item:hover {
            border-color: #2196f3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }

        .example-label {
            font-weight: 600;
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .example-sequence {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            color: #333;
            word-break: break-all;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 30px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .main-card {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .button-group {
                flex-direction: column;
            }

            .result-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 SSFGM-Model</h1>
            <p>基于多模态几何学习的抗菌肽预测系统</p>
        </div>

        <div class="main-card">
            <div class="status-info" id="statusInfo">
                <div class="status-indicator"></div>
                <span>正在检查系统状态...</span>
            </div>

            <form id="predictionForm">
                <div class="form-group">
                    <label for="sequence">
                        🧪 蛋白质序列 <span style="color: #f44336;">*</span>
                    </label>
                    <textarea 
                        id="sequence" 
                        name="sequence" 
                        placeholder="请输入氨基酸序列（支持标准20种氨基酸：ACDEFGHIKLMNPQRSTVWY）&#10;&#10;示例：MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
                        required
                    ></textarea>
                    <small style="color: #666; font-size: 0.9em;">
                        支持的氨基酸：A, C, D, E, F, G, H, I, K, L, M, N, P, Q, R, S, T, V, W, Y
                    </small>
                </div>

                <div class="form-group">
                    <label for="uniprot_id">
                        🔗 UniProt ID <span style="color: #999;">(可选)</span>
                    </label>
                    <input 
                        type="text" 
                        id="uniprot_id" 
                        name="uniprot_id" 
                        placeholder="例如：P12345"
                    >
                </div>

                <div class="button-group">
                    <button type="submit" class="btn-primary">
                        🚀 开始预测
                    </button>
                    <button type="button" class="btn-secondary" onclick="clearForm()">
                        🗑️ 清空表单
                    </button>
                </div>
            </form>

            <div id="result" class="result"></div>

            <div class="example-sequences">
                <h4>📋 示例序列</h4>
                <div class="example-item" onclick="useExample(0)">
                    <div class="example-label">抗菌肽示例 1</div>
                    <div class="example-sequence">MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG</div>
                </div>
                <div class="example-item" onclick="useExample(1)">
                    <div class="example-label">抗菌肽示例 2</div>
                    <div class="example-sequence">KWKLFKKIEKVGQNIRDGIIKAGPAVAVVGQATQIAK</div>
                </div>
                <div class="example-item" onclick="useExample(2)">
                    <div class="example-label">普通蛋白质示例</div>
                    <div class="example-sequence">MVLSPADKTNVKAAWGKVGAHAGEYGAEALERMFLSFPTTKTYFPHFDLSHGSAQVKGHGKKVADALTNAVAHVDDMPNALSALSDLHAHKLRVDPVNFKLLSHCLLVTLAAHLPAEFTPAVHASLDKFLASVSTVLTSKYR</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 SSFGM-Model | 基于深度学习的蛋白质功能预测</p>
        </div>
    </div>

    <script>
        // 示例序列数据
        const examples = [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "KWKLFKKIEKVGQNIRDGIIKAGPAVAVVGQATQIAK",
            "MVLSPADKTNVKAAWGKVGAHAGEYGAEALERMFLSFPTTKTYFPHFDLSHGSAQVKGHGKKVADALTNAVAHVDDMPNALSALSDLHAHKLRVDPVNFKLLSHCLLVTLAAHLPAEFTPAVHASLDKFLASVSTVLTSKYR"
        ];

        // 页面加载时检查系统状态
        window.addEventListener('load', function() {
            checkSystemStatus();
        });

        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                const statusInfo = document.getElementById('statusInfo');
                if (data.predictor_ready) {
                    statusInfo.innerHTML = `
                        <div class="status-indicator"></div>
                        <span>✅ 系统就绪 | 模型已加载 (${(data.model_file_size / (1024*1024)).toFixed(1)} MB)</span>
                    `;
                } else {
                    statusInfo.innerHTML = `
                        <div class="status-indicator" style="background: #f44336;"></div>
                        <span>❌ 系统异常 | 模型未加载</span>
                    `;
                    statusInfo.style.background = '#ffebee';
                    statusInfo.style.borderColor = '#f44336';
                }
            } catch (error) {
                const statusInfo = document.getElementById('statusInfo');
                statusInfo.innerHTML = `
                    <div class="status-indicator" style="background: #ff9800;"></div>
                    <span>⚠️ 无法连接到服务器</span>
                `;
                statusInfo.style.background = '#fff3e0';
                statusInfo.style.borderColor = '#ff9800';
            }
        }

        // 使用示例序列
        function useExample(index) {
            document.getElementById('sequence').value = examples[index];
            document.getElementById('sequence').focus();
        }

        // 清空表单
        function clearForm() {
            document.getElementById('sequence').value = '';
            document.getElementById('uniprot_id').value = '';
            document.getElementById('result').style.display = 'none';
        }

        // 表单提交处理
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const sequence = document.getElementById('sequence').value.trim();
            const uniprot_id = document.getElementById('uniprot_id').value.trim();
            const resultDiv = document.getElementById('result');
            
            // 验证输入
            if (!sequence) {
                showResult('error', '❌ 错误', '请输入蛋白质序列');
                return;
            }

            // 验证序列格式
            const validAminoAcids = /^[ACDEFGHIKLMNPQRSTVWY]+$/i;
            if (!validAminoAcids.test(sequence)) {
                showResult('error', '❌ 序列格式错误', '序列只能包含标准的20种氨基酸字符');
                return;
            }

            if (sequence.length > 500) {
                showResult('error', '❌ 序列过长', '序列长度不能超过500个氨基酸');
                return;
            }
            
            // 显示加载状态
            showResult('loading', '🔄 预测中', '正在分析蛋白质序列，请稍候...');
            
            try {
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sequence: sequence.toUpperCase(),
                        uniprot_id: uniprot_id || null
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const result = data.data;
                    const isAMP = result.prediction === 'AMP';
                    
                    const resultHTML = `
                        <h3>${isAMP ? '🦠' : '🧪'} 预测结果</h3>
                        <div class="result-grid">
                            <div class="result-item">
                                <strong>预测类别</strong>
                                <div class="result-value">${result.prediction}</div>
                            </div>
                            <div class="result-item">
                                <strong>置信度</strong>
                                <div class="result-value">${(result.confidence * 100).toFixed(2)}%</div>
                            </div>
                            <div class="result-item">
                                <strong>AMP 概率</strong>
                                <div class="result-value">${(result.probabilities.AMP * 100).toFixed(2)}%</div>
                            </div>
                            <div class="result-item">
                                <strong>NON-AMP 概率</strong>
                                <div class="result-value">${(result.probabilities['NON-AMP'] * 100).toFixed(2)}%</div>
                            </div>
                            <div class="result-item">
                                <strong>序列长度</strong>
                                <div class="result-value">${data.sequence_length} AA</div>
                            </div>
                            <div class="result-item">
                                <strong>处理时间</strong>
                                <div class="result-value">${data.processing_time}s</div>
                            </div>
                        </div>
                    `;
                    
                    showResult('success', '', resultHTML);
                } else {
                    showResult('error', '❌ 预测失败', data.error);
                }
            } catch (error) {
                showResult('error', '❌ 网络错误', `请求失败: ${error.message}`);
            }
        });

        // 显示结果
        function showResult(type, title, content) {
            const resultDiv = document.getElementById('result');
            
            if (type === 'loading') {
                resultDiv.innerHTML = `
                    <div class="loading-spinner"></div>
                    <strong>${title}</strong>
                    <p>${content}</p>
                `;
            } else {
                resultDiv.innerHTML = `
                    ${title ? `<h3>${title}</h3>` : ''}
                    ${content}
                `;
            }
            
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
            
            // 滚动到结果区域
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // 序列输入框实时验证
        document.getElementById('sequence').addEventListener('input', function(e) {
            const sequence = e.target.value.toUpperCase();
            const validAminoAcids = /^[ACDEFGHIKLMNPQRSTVWY]*$/;
            
            if (sequence && !validAminoAcids.test(sequence)) {
                e.target.style.borderColor = '#f44336';
                e.target.style.boxShadow = '0 0 0 3px rgba(244, 67, 54, 0.1)';
            } else {
                e.target.style.borderColor = '#e0e0e0';
                e.target.style.boxShadow = 'none';
            }
            
            // 自动转换为大写
            if (e.target.value !== sequence) {
                const cursorPos = e.target.selectionStart;
                e.target.value = sequence;
                e.target.setSelectionRange(cursorPos, cursorPos);
            }
        });
    </script>
</body>
</html>