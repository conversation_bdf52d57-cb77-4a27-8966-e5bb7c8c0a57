# requirements.txt
torch>=2.0.0,<3.0.0
torchvision>=0.15.0,<1.0.0
torchaudio>=2.0.0,<3.0.0
torch-geometric>=2.3.0,<3.0.0
torch-scatter>=2.1.0,<3.0.0
torch-sparse>=0.6.15,<1.0.0
torch-cluster>=1.6.0,<2.0.0
torch-spline-conv>=1.2.0,<2.0.0
numpy>=1.21.0,<2.0.0
biopython>=1.79,<2.0.0
scikit-learn>=1.1.0,<2.0.0
transformers>=4.21.0,<5.0.0
requests>=2.28.0,<3.0.0
flask>=2.2.0,<3.0.0
gunicorn>=20.1.0,<21.0.0