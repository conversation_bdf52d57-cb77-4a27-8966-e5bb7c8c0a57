# app.py (Cloud Run优化版本)
import os
import time
import logging
from flask import Flask, request, jsonify, send_from_directory
from src.predictor import SSFGMPredictor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 初始化预测器
try:
    logger.info("正在初始化SSFGM预测器...")
    predictor = SSFGMPredictor()
    logger.info("SSFGM预测器初始化成功")
except Exception as e:
    logger.error(f"预测器初始化失败: {e}")
    predictor = None

@app.route('/')
def index():
    """主页面"""
    try:
        return send_from_directory('static', 'index.html')
    except FileNotFoundError:
        return jsonify({
            'error': 'Index page not found',
            'message': '请确保 static/index.html 文件存在'
        }), 404

@app.route('/health')
def health_check():
    """健康检查 - Cloud Run要求"""
    model_status = "loaded" if predictor else "not_loaded"
    model_file_exists = os.path.exists('models/SSFGM-Model.pth')
    
    return jsonify({
        'status': 'healthy',
        'timestamp': time.time(),
        'predictor_ready': predictor is not None,
        'model_status': model_status,
        'model_file_exists': model_file_exists,
        'model_file_size': os.path.getsize('models/SSFGM-Model.pth') if model_file_exists else 0,
        'platform': 'Google Cloud Run'
    })

@app.route('/predict', methods=['POST'])
def predict():
    """预测接口"""
    if not predictor:
        return jsonify({
            'success': False,
            'error': '预测器未初始化，请检查模型文件'
        }), 500
    
    try:
        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        sequence = data.get('sequence', '').strip().upper()
        uniprot_id = data.get('uniprot_id', '').strip() or None
        
        # 验证输入
        if not sequence:
            return jsonify({
                'success': False,
                'error': '序列不能为空'
            }), 400
        
        if len(sequence) > 500:
            return jsonify({
                'success': False,
                'error': '序列过长，最大支持500个氨基酸'
            }), 400
        
        # 验证序列格式
        valid_amino_acids = set('ACDEFGHIKLMNPQRSTVWY')
        if not all(aa in valid_amino_acids for aa in sequence):
            return jsonify({
                'success': False,
                'error': '序列包含无效的氨基酸字符'
            }), 400
        
        # 执行预测
        start_time = time.time()
        result = predictor.predict(sequence, uniprot_id)
        processing_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'data': result,
            'sequence_length': len(sequence),
            'processing_time': round(processing_time, 2)
        })
        
    except Exception as e:
        logger.error(f"预测错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/info')
def api_info():
    """API信息"""
    return jsonify({
        'service': 'SSFGM-Model API',
        'version': '1.0.0',
        'description': '基于多模态几何学习的抗菌肽预测模型',
        'platform': 'Google Cloud Run',
        'model_file': 'models/SSFGM-Model.pth',
        'model_loaded': predictor is not None,
        'endpoints': {
            '/': 'Web界面',
            '/predict': 'POST - 预测接口',
            '/health': 'GET - 健康检查',
            '/api/info': 'GET - API信息'
        },
        'max_sequence_length': 500,
        'supported_amino_acids': 'ACDEFGHIKLMNPQRSTVWY'
    })

# 添加静态文件路由
@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

if __name__ == '__main__':
    # Cloud Run提供PORT环境变量
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)