# Dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc g++ curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 一次性安装所有依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu && \
    pip install --no-cache-dir torch-geometric && \
    pip install --no-cache-dir torch-scatter torch-sparse torch-cluster torch-spline-conv --find-links https://data.pyg.org/whl/torch-2.0.0+cpu.html && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 验证安装
RUN python -c "import torch; import torch_geometric; print(f'PyTorch: {torch.__version__}, PyG: {torch_geometric.__version__}')" && \
    ls -la models/ && \
    echo "Model file size: $(du -h models/SSFGM-Model.pth)"

# 环境变量
ENV PYTHONPATH=/app PYTHONUNBUFFERED=1 PORT=8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

EXPOSE 8080

CMD exec gunicorn --bind :$PORT --workers 1 --threads 8 --timeout 0 app:app