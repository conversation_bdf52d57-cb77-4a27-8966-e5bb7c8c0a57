# src/api_client.py
import os
import requests
import numpy as np
import time
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

class HuggingFaceAPIClient:
    """Hugging Face API客户端"""
    
    def __init__(self):
        self.hf_token = os.environ.get('HUGGINGFACE_TOKEN')
        if not self.hf_token:
            logger.warning("未设置HUGGINGFACE_TOKEN环境变量")
        
        self.protein_bert_url = "https://api-inference.huggingface.co/models/Rostlab/prot_bert"
        self.esm_url = "https://api-inference.huggingface.co/models/facebook/esm2_t33_650M_UR50D"
        
        self.headers = {"Authorization": f"Bearer {self.hf_token}"} if self.hf_token else {}
        self.timeout = 30
        self.max_retries = 3
    
    @lru_cache(maxsize=50)
    def get_protein_bert_embeddings(self, sequence):
        """获取ProteinBERT嵌入"""
        return self._call_api(self.protein_bert_url, sequence, "ProteinBERT")
    
    @lru_cache(maxsize=50)
    def get_esm_embeddings(self, sequence):
        """获取ESM嵌入"""
        return self._call_api(self.esm_url, sequence, "ESM")
    
    def _call_api(self, url, sequence, model_name):
        """调用API"""
        # 限制序列长度
        if len(sequence) > 400:
            sequence = sequence[:400]
            logger.warning(f"序列过长，截断到400个氨基酸")
        
        # 准备输入
        if model_name == "ProteinBERT":
            inputs = " ".join(sequence)  # ProteinBERT需要空格分隔
        else:
            inputs = sequence
        
        payload = {"inputs": inputs}
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"调用{model_name} API (尝试 {attempt + 1}/{self.max_retries})")
                
                response = requests.post(
                    url,
                    headers=self.headers,
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 处理不同的响应格式
                    if isinstance(result, list) and len(result) > 0:
                        embeddings = np.array(result[0])
                    elif isinstance(result, dict) and 'embeddings' in result:
                        embeddings = np.array(result['embeddings'])
                    else:
                        # 如果API返回格式不符合预期，创建随机嵌入
                        logger.warning(f"{model_name} API返回格式异常，使用随机嵌入")
                        embeddings = self._create_random_embeddings(sequence, model_name)
                    
                    # 确保嵌入维度正确
                    embeddings = self._normalize_embeddings(embeddings, sequence, model_name)
                    
                    logger.info(f"{model_name} API调用成功，嵌入形状: {embeddings.shape}")
                    return embeddings
                
                elif response.status_code == 503:
                    # 模型正在加载
                    wait_time = 20 * (attempt + 1)
                    logger.warning(f"{model_name} 模型正在加载，等待 {wait_time} 秒")
                    time.sleep(wait_time)
                    continue
                
                else:
                    logger.error(f"{model_name} API调用失败: {response.status_code}, {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"{model_name} API调用超时")
            except Exception as e:
                logger.error(f"{model_name} API调用异常: {e}")
            
            if attempt < self.max_retries - 1:
                time.sleep(5 * (attempt + 1))
        
        # 所有尝试都失败，返回随机嵌入
        logger.warning(f"{model_name} API调用失败，使用随机嵌入")
        return self._create_random_embeddings(sequence, model_name)
    
    def _create_random_embeddings(self, sequence, model_name):
        """创建随机嵌入作为后备"""
        seq_len = min(len(sequence), 160)
        
        if model_name == "ProteinBERT":
            # ProteinBERT: 1024维
            return np.random.normal(0, 0.1, (seq_len, 1024)).astype(np.float32)
        else:
            # ESM: 1280维
            return np.random.normal(0, 0.1, (seq_len, 1280)).astype(np.float32)
    
    def _normalize_embeddings(self, embeddings, sequence, model_name):
        """标准化嵌入维度"""
        target_length = min(len(sequence), 160)
        
        if model_name == "ProteinBERT":
            target_dim = 1024
        else:
            target_dim = 1280
        
        # 调整序列长度
        if len(embeddings) > target_length:
            embeddings = embeddings[:target_length]
        elif len(embeddings) < target_length:
            padding = np.zeros((target_length - len(embeddings), embeddings.shape[1]))
            embeddings = np.vstack([embeddings, padding])
        
        # 调整特征维度
        if embeddings.shape[1] != target_dim:
            if embeddings.shape[1] > target_dim:
                embeddings = embeddings[:, :target_dim]
            else:
                padding = np.zeros((embeddings.shape[0], target_dim - embeddings.shape[1]))
                embeddings = np.hstack([embeddings, padding])
        
        return embeddings.astype(np.float32)