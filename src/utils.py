# src/utils.py
import numpy as np
import torch
from torch_geometric.data import Data
from scipy.spatial.distance import pdist, squareform

def create_one_hot_encoding(sequence, max_length, amino_acids='ACDEFGHIKLMNPQRSTVWY'):
    """创建氨基酸序列的one-hot编码"""
    aa_to_idx = {aa: i for i, aa in enumerate(amino_acids)}
    
    # 限制序列长度
    sequence = sequence[:max_length]
    
    # 创建one-hot编码
    encoded = np.zeros((max_length, len(amino_acids)), dtype=np.float32)
    
    for i, aa in enumerate(sequence):
        if aa in aa_to_idx:
            encoded[i, aa_to_idx[aa]] = 1.0
    
    return encoded

def prepare_graph_data(features, sequence, cutoff=10.0):
    """准备图数据"""
    # 创建虚拟的3D坐标（因为我们没有真实的PDB结构）
    seq_len = min(len(sequence), features.shape[0])
    
    # 生成线性排列的坐标
    coords = np.array([[i * 3.8, 0, 0] for i in range(seq_len)], dtype=np.float32)
    
    # 计算距离矩阵
    distances = squareform(pdist(coords))
    
    # 创建边
    edge_indices = []
    edge_weights = []
    
    for i in range(seq_len):
        for j in range(i + 1, seq_len):
            if distances[i, j] <= cutoff:
                edge_indices.append([i, j])
                edge_indices.append([j, i])  # 无向图
                weight = 1.0 / (1.0 + distances[i, j])  # 距离权重
                edge_weights.extend([weight, weight])
    
    # 转换为PyTorch张量
    if edge_indices:
        edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
        edge_attr = torch.tensor(edge_weights, dtype=torch.float)
    else:
        # 如果没有边，创建自环
        edge_index = torch.tensor([[i, i] for i in range(seq_len)], dtype=torch.long).t().contiguous()
        edge_attr = torch.ones(seq_len, dtype=torch.float)
    
    # 特征张量
    x = torch.tensor(features[:seq_len], dtype=torch.float)
    
    # 创建图数据
    data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
    
    return data