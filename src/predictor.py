# src/predictor.py (完整修复版本)
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from torch_geometric.data import Data
from functools import lru_cache
import logging
from .api_client import HuggingFaceAPIClient
from .utils import create_one_hot_encoding, prepare_graph_data

logger = logging.getLogger(__name__)

# 首先定义所有模型类
class ImprovedGCN(torch.nn.Module):
    """改进的图卷积网络"""
    def __init__(self, num_features, num_classes, heads=4, dropout=0.5):
        super(ImprovedGCN, self).__init__()
        self.conv1 = GCNConv(num_features, 1024)
        self.conv2 = GCNConv(1024, 512)
        self.conv3 = GCNConv(512, 256)
        self.conv4 = GCNConv(256, 128)
        self.conv5 = GCNConv(128, 64)
        self.conv6 = GCNConv(64, 32)
        
        self.attn1 = GATConv(32, 16 // heads, heads=heads, concat=True)
        self.fc = nn.Linear(16, num_classes)
        self.dropout = dropout

    def forward(self, data, return_features=False):
        x, edge_index, edge_attr, batch = data.x, data.edge_index, data.edge_attr, data.batch
        
        x = F.relu(self.conv1(x, edge_index, edge_weight=edge_attr))
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = F.relu(self.conv2(x, edge_index, edge_weight=edge_attr))
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = F.relu(self.conv3(x, edge_index, edge_weight=edge_attr))
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = F.relu(self.conv4(x, edge_index, edge_weight=edge_attr))
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = F.relu(self.conv5(x, edge_index, edge_weight=edge_attr))
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = F.relu(self.conv6(x, edge_index, edge_weight=edge_attr))
        x = F.elu(self.attn1(x, edge_index, edge_attr=edge_attr))
        x = global_mean_pool(x, batch)
        
        if return_features:
            return x
        x = self.fc(x)
        return F.log_softmax(x, dim=1)

class MaSIF_site_PyTorch(nn.Module):
    """MaSIF表面特征模型"""
    def __init__(self, n_thetas, n_rhos, n_feat, n_rotations, dropout_rate=0.5):
        super(MaSIF_site_PyTorch, self).__init__()
        self.n_thetas = n_thetas
        self.n_rhos = n_rhos
        self.n_feat = n_feat
        self.n_rotations = n_rotations

        # 参数
        self.mu_rho = nn.Parameter(torch.Tensor(self.n_rotations, 1))
        self.sigma_rho = nn.Parameter(torch.Tensor(self.n_rotations, 1))
        self.mu_theta = nn.Parameter(torch.Tensor(self.n_rotations, 1))
        self.sigma_theta = nn.Parameter(torch.Tensor(self.n_rotations, 1))

        # 初始化参数
        nn.init.uniform_(self.mu_rho, 0, 1)
        nn.init.constant_(self.sigma_rho, 0.5)
        nn.init.uniform_(self.mu_theta, 0, 2 * np.pi)
        nn.init.constant_(self.sigma_theta, 0.5)

        # 层
        self.avgpool1d = nn.AvgPool1d(kernel_size=6, stride=5)
        self.fc1 = nn.Linear(40840, 2)

    def forward(self, input_feat, rho_coords, theta_coords, mask, return_features=False):
        batch_size, n_vertices, num_points, n_feat = input_feat.size()
        input_feat = input_feat.mean(dim=2)

        output_feats = []
        for k in range(self.n_rotations):
            rotated_theta_coords = theta_coords + k * 2 * np.pi / self.n_rotations
            rotated_theta_coords %= 2 * np.pi

            rho_gauss = torch.exp(-torch.square(rho_coords - self.mu_rho[k]) / (2 * torch.square(self.sigma_rho[k]) + 1e-5))
            theta_gauss = torch.exp(-torch.square(rotated_theta_coords - self.mu_theta[k]) / (2 * torch.square(self.sigma_theta[k]) + 1e-5))

            gauss_activations = rho_gauss * theta_gauss * mask
            gauss_activations /= torch.sum(gauss_activations, dim=1, keepdim=True) + 1e-5

            gauss_activations = gauss_activations.unsqueeze(3)
            gauss_activations = gauss_activations.expand(-1, -1, -1, n_feat)

            gauss_desc = torch.sum(gauss_activations * input_feat.unsqueeze(2), dim=2)
            output_feats.append(gauss_desc)

        output_feats = torch.cat(output_feats, dim=2)
        output_feats = output_feats.view(batch_size, -1)
        
        if return_features:
            return output_feats
        
        output_feats = self.fc1(output_feats)
        return F.log_softmax(output_feats, dim=1)

class FusionModel(nn.Module):
    """融合模型"""
    def __init__(self, model_gcn, model_masif, output_features, num_classes):
        super(FusionModel, self).__init__()
        self.model_gcn = model_gcn
        self.model_masif = model_masif
        self.reduce_masif = nn.Linear(40840, 16)
        self.fusion_layer = nn.Linear(output_features, 2)

    def forward(self, data_gcn, data_masif):
        gcn_features = self.model_gcn(data_gcn, return_features=True)
        
        input_feat = data_masif['input_feat']
        rho_coords = data_masif['rho_coords']
        theta_coords = data_masif['theta_coords']
        mask = data_masif['mask']
        
        masif_features = self.model_masif(input_feat, rho_coords, theta_coords, mask, return_features=True)
        masif_features = F.relu(self.reduce_masif(masif_features))
        
        combined_features = torch.cat((gcn_features, masif_features), dim=1)
        combined_features = F.relu(combined_features)
        
        output = self.fusion_layer(combined_features)
        return F.log_softmax(output, dim=1)

# 然后定义预测器类
class SSFGMPredictor:
    """SSFGM模型预测器"""
    
    def __init__(self):
        self.device = torch.device('cpu')  # Cloud Run使用CPU
        self.max_length = 160
        
        # 初始化API客户端
        try:
            self.api_client = HuggingFaceAPIClient()
        except Exception as e:
            logger.error(f"API客户端初始化失败: {e}")
            self.api_client = None
        
        # 初始化模型
        self.model = self._load_model()
        
        logger.info("SSFGM预测器初始化完成")
    
    def _load_model(self):
        """加载预训练模型"""
        try:
            logger.info("开始初始化模型组件...")
            
            # 初始化模型组件
            model_gcn = ImprovedGCN(num_features=3604, num_classes=2)
            logger.info("ImprovedGCN 初始化成功")
            
            model_masif = MaSIF_site_PyTorch(n_thetas=16, n_rhos=5, n_feat=5, n_rotations=8)
            logger.info("MaSIF_site_PyTorch 初始化成功")
            
            # 创建融合模型
            fusion_model = FusionModel(model_gcn, model_masif, 32, num_classes=2)
            logger.info("FusionModel 初始化成功")
            
            # 模型文件路径
            model_path = 'models/SSFGM-Model.pth'
            
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                logger.warning(f"模型文件不存在: {model_path}，使用随机权重")
            else:
                # 检查文件大小
                file_size = os.path.getsize(model_path)
                logger.info(f"模型文件大小: {file_size / (1024*1024):.2f} MB")
                
                # 尝试加载模型权重
                try:
                    state_dict = torch.load(model_path, map_location=self.device)
                    fusion_model.load_state_dict(state_dict, strict=False)
                    logger.info(f"成功加载预训练模型: {model_path}")
                except Exception as load_error:
                    logger.error(f"加载模型权重失败: {load_error}")
                    logger.warning("使用随机初始化权重")
            
            fusion_model.eval()
            return fusion_model
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            import traceback
            logger.error(f"完整错误信息: {traceback.format_exc()}")
            raise
    
    @lru_cache(maxsize=50)
    def predict(self, sequence, uniprot_id=None):
        """预测抗菌肽"""
        try:
            logger.info(f"开始预测序列长度: {len(sequence)}")
            
            # 1. 获取序列特征
            features = self._extract_features(sequence)
            
            # 2. 准备模型输入
            model_input = self._prepare_model_input(features, sequence)
            
            # 3. 模型推理
            with torch.no_grad():
                output = self.model(model_input['gcn_data'], model_input['masif_data'])
                probabilities = torch.softmax(output, dim=1)
                
                prediction = torch.argmax(probabilities, dim=1).item()
                confidence = probabilities[0][prediction].item()
                
                result = {
                    'prediction': 'AMP' if prediction == 1 else 'NON-AMP',
                    'confidence': float(confidence),
                    'probabilities': {
                        'AMP': float(probabilities[0][1]),
                        'NON-AMP': float(probabilities[0][0])
                    }
                }
                
                logger.info(f"预测完成: {result['prediction']}, 置信度: {result['confidence']:.3f}")
                return result
                
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    def _extract_features(self, sequence):
        """提取序列特征"""
        features = {}
        
        # 1. One-hot编码
        features['one_hot'] = create_one_hot_encoding(sequence, self.max_length)
        
        # 2. ProteinBERT特征
        if self.api_client:
            try:
                features['protein_bert'] = self.api_client.get_protein_bert_embeddings(sequence)
            except Exception as e:
                logger.warning(f"ProteinBERT API调用失败: {e}")
                features['protein_bert'] = np.zeros((self.max_length, 1024))
        else:
            features['protein_bert'] = np.zeros((self.max_length, 1024))
        
        # 3. ESM特征
        if self.api_client:
            try:
                features['esm'] = self.api_client.get_esm_embeddings(sequence)
            except Exception as e:
                logger.warning(f"ESM API调用失败: {e}")
                features['esm'] = np.zeros((self.max_length, 1280))
        else:
            features['esm'] = np.zeros((self.max_length, 1280))
        
        return features
    
    def _prepare_model_input(self, features, sequence):
        """准备模型输入"""
        # 组合特征
        combined_features = np.concatenate([
            features['protein_bert'],
            features['esm'],
            features['one_hot']
        ], axis=1)
        
        # 创建图数据
        gcn_data = prepare_graph_data(combined_features, sequence)
        
        # 创建虚拟MaSIF数据
        batch_size = 1
        n_vertices = min(len(sequence), self.max_length)
        masif_data = {
            'input_feat': torch.randn(batch_size, n_vertices, 200, 5),
            'rho_coords': torch.randn(batch_size, n_vertices, 200),
            'theta_coords': torch.randn(batch_size, n_vertices, 200),
            'mask': torch.ones(batch_size, n_vertices, 200)
        }
        
        return {
            'gcn_data': gcn_data,
            'masif_data': masif_data
        }