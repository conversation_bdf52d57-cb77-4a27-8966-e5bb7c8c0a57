# cloudbuild.yaml
steps:
  # 构建Docker镜像
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build', 
      '-t', 'gcr.io/$PROJECT_ID/ssfgm-model:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/ssfgm-model:latest',
      '.'
    ]
  
  # 推送镜像到Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ssfgm-model:$BUILD_ID']
  
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ssfgm-model:latest']
  
  # 部署到Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'ssfgm-model',
      '--image', 'gcr.io/$PROJECT_ID/ssfgm-model:$BUILD_ID',
      '--platform', 'managed',
      '--region', 'us-central1',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '1',
      '--timeout', '300',
      '--concurrency', '10',
      '--max-instances', '5',
      '--set-env-vars', 'PORT=8080'
    ]

# 构建超时时间
timeout: '1200s'

# 构建选项
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'